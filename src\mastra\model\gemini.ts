import { createOpenAI } from "@ai-sdk/openai";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateText } from "ai";

export const gemini = createGoogleGenerativeAI({
  baseURL:
    "http://152.53.171.95:3001/proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent?key=sk-PfY4IVyb9AeaVMf9Db2Haur0DneZkqelW8fU6ifCGDe3-8R2",
  apiKey: "sk-PfY4IVyb9AeaVMf9Db2Haur0DneZkqelW8fU6ifCGDe3-8R2"
  // compatibility: "compatible"
})("gemini-2.5-pro");

const result = await generateText({
  model: gemini,
  prompt: "Hello, how are you?"
});
console.log(result);

// export const gemini = createGoogleGenerativeAI({
//   apiKey: "AIzaSyAAmJa405DjZIcWi-e6Q-8IcoiQiDvAQk0"
// })("gemini-2.5-pro");
