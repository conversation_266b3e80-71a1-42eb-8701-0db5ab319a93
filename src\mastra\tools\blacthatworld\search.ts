import { retryFetch } from "../../proxies/index.js";
import { proxyManager } from "../../proxies/manager.js";
import * as cheerio from "cheerio";
import fs from "fs";

interface NodeInfo {
  id: string;
  name: string;
  level: number;
  children: NodeInfo[];
  parent?: NodeInfo;
}

const rawNodes = [
  ["", "All forums"],
  ["23", "BlackHatWorld"],
  ["242", "   Newbie Guide"],
  ["261", "     Brand New to BHW"],
  ["262", "     Newbies"],
  ["263", "     Newbie+"],
  ["265", "     Marketplace Sellers"],
  ["299", "     New to Marketplace"],
  ["24", "   Introductions"],
  ["25", "   BlackHat Lounge"],
  ["26", "   Forum Suggestions & Feedback"],
  ["226", "     BHW Beta Testers"],
  ["31", "   Dispute Resolution"],
  ["303", "   Free Review Copies For Marketplace Approvals"],
  ["73", "The Marketplace"],
  ["203", "   BHW Marketplace rules and how to post"],
  ["310", "   Account Selling / Renting Services"],
  ["193", "   Affiliate programs - CPA networks"],
  ["194", "   Content / Copywriting"],
  ["195", "   Domains & websites for sale"],
  ["308", "   eBooks, methods and courses"],
  ["196", "   Hosting"],
  ["197", "   Hot Deals"],
  ["198", "   Images, logos & videos"],
  ["18", "   Misc"],
  ["112", "   Proxies For Sale"],
  ["43", "   SEO - Link building"],
  ["199", "   SEO - Other"],
  ["206", "   SEO - Packages"],
  ["200", "   Social Media"],
  ["302", "   Social Media - Panels"],
  ["201", "   Web Design"],
  ["1", "Black Hat SEO"],
  ["252", "   AI - Artificial Intelligence in Digital Marketing"],
  ["28", "   Black Hat SEO"],
  ["9", "   Black Hat SEO Tools"],
  ["3", "   Blogging"],
  ["2", "   Cloaking and Content Generators"],
  ["101", "   Proxies"],
  ["103", "     Proxy Lists"],
  ["280", "   Voice Search"],
  ["270", "Social Media"],
  ["32", "   General Social Chat"],
  ["86", "   FaceBook"],
  ["215", "   Instagram"],
  ["214", "   Linkedin"],
  ["87", "   Myspace"],
  ["211", "   Pinterest"],
  ["301", "   Reddit"],
  ["279", "   TikTok"],
  ["217", "   Tumblr"],
  ["216", "   Weibo"],
  ["210", "   X (formerly Twitter)"],
  ["77", "   YouTube"],
  ["95", "White Hat SEO"],
  ["168", "   Copywriting & Sales Persuasion"],
  ["173", "     Downloads - Copywriting & Sales"],
  ["53", "   Domain Names & Parking"],
  ["169", "   Graphic Design"],
  ["171", "     Downloads - Graphic design"],
  ["108", "   Link Building"],
  ["209", "   Local SEO"],
  ["224", "   Online Reputation Management (ORM)"],
  ["170", "   Video Production"],
  ["172", "     Downloads - Video production"],
  ["94", "   Web Hosting"],
  ["30", "   White Hat SEO"],
  ["11", "Making Money"],
  ["107", "   Associated Content & Writing Articles"],
  ["15", "   Affiliate Programs"],
  ["66", "     Clickbank"],
  ["71", "     CJ Affiliate"],
  ["72", "     Other Affiliate Programs"],
  ["225", "     Zero Parallel & T.UK"],
  ["96", "   Business & Tax Advice"],
  ["50", "   CPA"],
  ["218", "   CryptoCurrency"],
  ["68", "   Dropshipping & Wholesale Hookups"],
  ["69", "   Ebay"],
  ["76", "   Hire a Freelancer"],
  ["65", "   Joint Ventures"],
  ["12", "   Making Money"],
  ["175", "   Media Buying"],
  ["106", "   Membership Sites"],
  ["158", "   Mobile Marketing"],
  ["167", "   My Journey Discussions"],
  ["208", "   New Markets"],
  ["132", "   Offline Marketing"],
  ["13", "   Pay Per Click"],
  ["83", "     Google Ads"],
  ["219", "     Facebook"],
  ["93", "     Yahoo & Bing MSN"],
  ["85", "     Other PPC Networks"],
  ["125", "     General PPC Discussion"],
  ["205", "   Pay Per Install"],
  ["102", "   Pay Per View"],
  ["141", "   Site Flipping"],
  ["75", "   Torrents"],
  ["174", "   Freebies / Giveaways"],
  ["165", "   Service Reviews & Beta Testers Help Wanted"],
  ["40", "Programming & Web Design"],
  ["79", "   Programming"],
  ["128", "     General Programming Chat"],
  ["60", "     C, C++, C#"],
  ["61", "     Visual Basic 6"],
  ["62", "     Visual Basic .NET"],
  ["131", "     Other Languages"],
  ["59", "   Scripting"],
  ["129", "     General Scripting Chat"],
  ["80", "     HTML & JavaScript"],
  ["127", "     PHP & Perl"],
  ["130", "     Other Scripting Languages"],
  ["126", "   Web Design"],
  ["212", "Conferences / Events"],
  ["207", "   BHW Events"],
  ["221", "     UnGagged"],
  ["269", "       UnGagged Los Angeles"],
  ["235", "       UnGagged Las Vegas"],
  ["222", "       UnGagged London"],
  ["213", "       UnGagged 2014 - Las Vegas"]
];

// 构建节点树结构
function buildNodeTree(): { tree: NodeInfo[]; nameToIdMap: Map<string, string> } {
  const tree: NodeInfo[] = [];
  const nameToIdMap = new Map<string, string>();
  const nodeStack: NodeInfo[] = [];

  for (const [id, name] of rawNodes) {
    // 计算缩进级别（每两个空格为一级）
    const level = Math.floor((name.length - name.trimStart().length) / 2);
    const cleanName = name.trim();

    const node: NodeInfo = {
      id,
      name: cleanName,
      level,
      children: []
    };

    // 建立名称到ID的映射
    nameToIdMap.set(cleanName, id);

    // 根据级别确定父子关系
    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
      nodeStack.pop();
    }

    if (nodeStack.length === 0) {
      // 根节点
      tree.push(node);
    } else {
      // 子节点
      const parent = nodeStack[nodeStack.length - 1];
      node.parent = parent;
      parent.children.push(node);
    }

    nodeStack.push(node);
  }

  return { tree, nameToIdMap };
}

// 获取节点树的文字描述
function getNodesDescription(): string {
  const { tree } = buildNodeTree();

  function formatNode(node: NodeInfo, indent: string = ""): string {
    let result = `${indent}- ${node.name}`;
    if (node.id) {
      result += ` (ID: ${node.id})`;
    }
    result += "\n";

    for (const child of node.children) {
      result += formatNode(child, indent + "  ");
    }

    return result;
  }

  let description = "BlackHatWorld Forum Nodes Structure:\n\n";
  for (const rootNode of tree) {
    description += formatNode(rootNode);
  }

  return description;
}

// 根据节点名称获取节点ID
function getNodeIds(nodeNames: string[]): string[] {
  const { nameToIdMap } = buildNodeTree();
  const nodeIds: string[] = [];

  for (const name of nodeNames) {
    const id = nameToIdMap.get(name);
    if (id) {
      nodeIds.push(id);
    } else {
      console.warn(`Node not found: ${name}`);
    }
  }

  return nodeIds;
}

// 生成与写死数据完全相同格式的 multipart form data
function generateFormBody(
  keywords: string,
  nodeIds: string[],
  newer_than: string,
  older_than: string,
  minReplies: number,
  order: string
): { body: string; contentType: string; cookies: string } {
  const sessionPath = "./.blackhatworld_session.json";
  const session = fs.existsSync(sessionPath) ? JSON.parse(fs.readFileSync(sessionPath, "utf-8") || "{}") : {};
  // 使用与写死数据相同的 boundary 和 token
  const boundary = "WebKitFormBoundarySyD8ceA39bAgcl6b";
  // const xfToken = "1753945290,0c528ca97f1dfd01e19cc851e3797a6b";
  const xfToken = session?.xfToken || "1754104794,5893fd58a655a4f52b5538eaf5f58e1d";

  let body = "";

  // 添加字段的辅助函数
  const addField = (name: string, value: string) => {
    body += `------${boundary}\r\n`;
    body += `Content-Disposition: form-data; name="${name}"\r\n\r\n`;
    body += `${value}\r\n`;
  };

  // 按照写死数据的顺序添加字段
  addField("_xfToken", xfToken);
  addField("keywords", keywords);
  addField("c[users]", "");
  addField("c[newer_than]", newer_than || "");
  addField("c[older_than]", older_than || "");

  // 如果有节点，添加child_nodes和节点列表
  if (nodeIds.length > 0) {
    addField("c[child_nodes]", "1");
    nodeIds.forEach((nodeId, index) => {
      addField(`c[nodes][${index}]`, nodeId);
    });
  }

  addField("c[min_reply_count]", minReplies.toString());

  addField("order", order || "relevance");
  addField("search_type", ""); // 写死数据中这个字段是空的
  addField("_xfResponseType", "json");
  addField("_xfWithData", "1");
  addField("_xfRequestUri", "/search");
  addField("_xfToken", xfToken); // 重复添加 token

  // 结束 boundary
  body += `------${boundary}--\r\n`;

  return {
    body,
    cookies: session?.cookies,
    contentType: `multipart/form-data; boundary=----${boundary}`
  };
}

async function refreshCookie() {
  await retryFetch((dispatcher, reportError) => {
    const token = process.env.SCRAPINGANT_TOKEN || "********************************";
    return fetch(
      `https://api.scrapingant.com/v2/extended?url=https%3A%2F%2Fwww.blackhatworld.com%2Fsearch&x-api-key=${token}&return_page_source=true`
    )
      .then(res => res.json())
      .then(res => {
        if (!res.html) {
          reportError(new Error("No HTML found"));
        }
        const $ = cheerio.load(res.html);
        const xfToken = $('[name="_xfToken"]').val();
        const session = {
          cookies: res.cookies,
          xfToken
        };
        fs.writeFileSync("./.blackhatworld_session.json", JSON.stringify(session, null, 2));
        return res;
      });
  });
}

//  https://www.blackhatworld.com/search/22847739/?q=content+farm&t=post&c[child_nodes]=1&c[min_reply_count]=10&c[newer_than]=2025-01-14&c[older_than]=2025-07-27&c[nodes][0]=26&c[nodes][1]=226&c[nodes][2]=31&c[nodes][3]=303&c[nodes][4]=73&c[older_than]=2025-07-27&o=relevance&g=1
// 规则：1. 当 nodes 存在时存在child_nodes，older_than， newer_than  older_than 可为空， o 取值有 relevance  date  replies

/**
 * 搜索 BlackHatWorld 论坛
 * @param keywords 搜索关键词
 * @param nodeNames 节点名称数组（使用节点的显示名称，不是ID）
 * @param newer_than 开始日期 (YYYY-MM-DD)
 * @param older_than 结束日期 (YYYY-MM-DD)
 * @param order 排序方式: 'relevance' | 'date' | 'replies'
 * @returns Promise<string> 返回搜索结果的重定向URL
 */
async function search(
  keywords: string,
  nodeNames: string[] = [],
  newer_than: string = "",
  older_than: string = "",
  minReplies: number = 5,
  order: string = "relevance"
): Promise<string> {
  // 将节点名称转换为节点ID
  const nodeIds = getNodeIds(nodeNames);

  // 生成表单数据

  const data: any = await retryFetch((dispatcher, reportError) => {
    const { body, contentType, cookies } = generateFormBody(keywords, nodeIds, newer_than, older_than, minReplies, order);

    const options = {
      // @ts-ignore
      dispatcher,
      headers: {
        "content-type": contentType,
        accept: "application/json",
        "accept-language": "zh-CN,zh;q=0.9",
        priority: "u=1, i",
        "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
        "sec-ch-ua-arch": '"x86"',
        "sec-ch-ua-bitness": '"64"',
        "sec-ch-ua-full-version": '"140.0.7326.0"',
        "sec-ch-ua-full-version-list":
          '"Chromium";v="140.0.7326.0", "Not=A?Brand";v="24.0.0.0", "Google Chrome";v="140.0.7326.0"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-model": '""',
        "sec-ch-ua-platform": '"Windows"',
        "sec-ch-ua-platform-version": '"10.0.0"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        // cookie:
        //   "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=-E-1gewjr4zp762E; xf_session=oadHVcJQ2luGfnxTYJDkfK73tBYcDMMc; _ga_VH2PZEKYEE=GS2.1.s1753945296$o14$g1$t1753945302$j54$l0$h0",
        cookie:
          cookies ||
          "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=amZDq0x0H4_Mk5cS; xf_session=A7w4xuheKQwpmNMhqjOVoslaBAPNpzAt; _ga_VH2PZEKYEE=GS2.1.s1754104758$o16$g1$t1754104796$j22$l0$h0",

        Referer: "https://www.blackhatworld.com/search/?type=post"
      },
      body: body,

      method: "POST"
    };

    return fetch("https://www.blackhatworld.com/search/search", options)
      .then(res => res.json())
      .then(async res => {
        if (res.status === "error") {
          await refreshCookie();
          reportError(new Error("Refresh cookie error"));
        }
        return res;
      });
  });

  return data.redirect;
}

// 导出函数供外部使用
export { search, getNodesDescription };

