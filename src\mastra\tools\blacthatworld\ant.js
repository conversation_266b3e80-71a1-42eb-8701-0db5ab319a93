import http from "https";

var options = {
  method: "GET",
  hostname: "api.scrapingant.com",
  port: null,
  path: "/v2/extended?url=https%3A%2F%2Fwww.blackhatworld.com%2Fsearch&x-api-key=042c1d766f834a46b51f31b4edca4318&return_page_source=true",
  headers: {
    useQueryString: true
  }
};

var req = http.request(options, function (res) {
  var chunks = [];
  res.on("data", function (chunk) {
    chunks.push(chunk);
  });

  res.on("end", function () {
    var body = Buffer.concat(chunks);
    console.log(body.toString());
  });
});

req.end();
